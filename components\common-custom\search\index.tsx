"use client"

/**
 * 搜索组件集合
 *
 * 按照新规范重新导出所有搜索相关组件和类型
 */

// ============================================================================
// 主组件导出
// ============================================================================

export { SearchInput } from "./search-input"
export { SearchResults } from "./search-results"
export { GlobalSearch } from "./global-search"
export { CommandBar } from "./command-bar"

// ============================================================================
// 子组件导出
// ============================================================================

export { SearchSuggestions } from "./search-suggestions"
export { SearchTags } from "./search-tags"
export { AdvancedSearch } from "./advanced-search"
export { SemanticSearch } from "./semantic-search"

// ============================================================================
// 类型导出
// ============================================================================

// 从本地types文件导出所有类型
export * from "./types"

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的搜索输入尺寸
 */
export const SEARCH_INPUT_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的搜索建议类型
 */
export const SEARCH_SUGGESTION_TYPES = ['query', 'filter', 'recent', 'popular'] as const

/**
 * 支持的搜索过滤器类型
 */
export const SEARCH_FILTER_TYPES = ['text', 'select', 'checkbox', 'date', 'number'] as const

/**
 * 默认搜索配置
 */
export const DEFAULT_SEARCH_CONFIG = {
  placeholder: '搜索...',
  size: 'md' as const,
  clearable: true,
  maxResults: 10,
  maxSuggestions: 10,
} as const