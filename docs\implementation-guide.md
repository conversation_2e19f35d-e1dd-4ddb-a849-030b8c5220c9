# 组件开发规范实施指南

本文档提供了基于组件开发规范的具体实施步骤和最佳实践，帮助团队快速上手并应用新的开发规范。

## 1. 快速开始

### 1.1 环境准备
确保你的开发环境已经配置了以下工具：
- Node.js 18+
- TypeScript 5.0+
- ESLint + Prettier
- VS Code（推荐）+ 相关扩展

### 1.2 项目结构理解
```
project-root/
├── components/
│   ├── ui/                     # shadcn/ui 基础组件
│   ├── common-custom/          # 通用业务组件
│   ├── project-custom/         # 项目特定组件
│   └── component-detail/       # 预览系统
├── app/examples/              # 组件示例页面
├── types/                     # 全局类型定义
└── docs/                      # 文档和模板
```

## 2. 创建新组件

### 2.1 判断组件类型
在开始开发前，先判断你要创建的是单文件组件还是复杂组件：

**单文件组件特征**：
- 功能相对简单，代码量 < 200 行
- 类型定义 < 50 行
- 不包含子组件
- 独立的功能单元

**复杂组件特征**：
- 包含多个子组件
- 代码量 > 200 行
- 复杂的类型定义
- 需要多个文件组织

### 2.2 单文件组件开发步骤

#### 步骤1：创建组件文件
```bash
# 在 components/common-custom/ 目录下创建组件文件
touch components/common-custom/my-component.tsx
```

#### 步骤2：使用模板开发
参考 `docs/examples/single-file-component-template.tsx` 模板：

```tsx
"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

// 类型定义直接在组件文件内
export interface MyComponentProps {
  /**
   * 组件标题
   */
  title: string
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
}

export function MyComponent({
  title,
  disabled = false,
  className,
}: MyComponentProps) {
  return (
    <div className={cn("my-component", className)}>
      <h3>{title}</h3>
      {/* 组件实现 */}
    </div>
  )
}
```

#### 步骤3：创建示例页面
```bash
# 创建示例目录
mkdir -p app/examples/my-component-example/examples
```

创建 `app/examples/my-component-example/page.tsx`：
```tsx
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { MyComponent } from "@/components/common-custom/my-component"
import { allExamples } from "./examples"

export default function MyComponentPreview() {
  return (
    <ComponentPreviewContainer
      title="我的组件 MyComponent"
      description="组件功能描述"
      whenToUse="何时使用这个组件"
      examples={allExamples}
      apiDocs={<MyComponentApiDocs />}
    />
  );
}

function MyComponentApiDocs() {
  // API文档实现
}
```

#### 步骤4：创建示例代码
创建 `app/examples/my-component-example/examples/index.ts`：
```tsx
export const basicExample = {
  id: "basic-example",
  title: "基础用法",
  description: "组件的基本使用方式",
  code: `
import React from "react";
import { MyComponent } from "@/components/common-custom/my-component";

function BasicExample() {
  return <MyComponent title="示例标题" />;
}

render(<BasicExample />);
  `,
  scope: { MyComponent, React },
}

export const allExamples = [basicExample]
```

#### 步骤5：配置导航
在 `components/navigation/main-layout.tsx` 中添加：
```tsx
{
  title: "我的组件",
  href: "/examples/my-component-example",
  isNew: true,
}
```

### 2.3 复杂组件开发步骤

#### 步骤1：创建组件目录结构
```bash
mkdir -p components/common-custom/my-complex-component
cd components/common-custom/my-complex-component

# 创建必要文件
touch index.tsx types.ts constants.ts utils.ts
touch my-complex-component.tsx
touch my-complex-component-header.tsx
touch my-complex-component-content.tsx
```

#### 步骤2：定义类型
参考 `docs/examples/complex-component-template/types.ts` 模板，在 `types.ts` 中定义所有类型。

#### 步骤3：实现组件
参考 `docs/examples/complex-component-template/index.tsx` 模板，实现各个组件文件。

#### 步骤4：创建导出文件
在 `index.tsx` 中统一导出所有组件和类型：
```tsx
export { MyComplexComponent } from "./my-complex-component"
export { MyComplexComponentHeader } from "./my-complex-component-header"
export * from "./types"
```

#### 步骤5：创建示例和文档
按照单文件组件的步骤3-5创建示例页面和配置导航。

## 3. 预览代码管理

### 3.1 推荐的代码组织方式
```
app/examples/component-example/
├── page.tsx                   # 主页面
└── examples/                  # 示例代码目录
    ├── basic.ts              # 基础示例
    ├── advanced.ts           # 高级示例
    ├── customization.ts      # 自定义示例
    └── index.ts              # 统一导出
```

### 3.2 示例代码编写规范
```tsx
// examples/basic.ts
export const basicExample = {
  id: "basic-example",
  title: "基础用法",
  description: "组件的基本使用方式",
  code: `
import React from "react";
import { Component } from "@/components/common-custom/component";

function BasicExample() {
  return <Component title="示例" />;
}

render(<BasicExample />);
  `,
  scope: { Component, React },
}
```

### 3.3 示例代码最佳实践
- 每个示例都要完整可运行
- 包含必要的导入语句
- 使用有意义的示例数据
- 提供合适的 scope 对象
- 添加清晰的描述

## 4. 常见问题解决

### 4.1 类型定义问题
**问题**：不确定类型应该放在哪里
**解决**：
- 单文件组件：类型直接在组件文件内定义
- 复杂组件：类型在组件目录的 `types.ts` 文件中定义
- 全局通用类型：在 `types/` 目录中定义

### 4.2 示例代码不工作
**问题**：示例代码在预览中报错
**解决**：
- 检查 scope 对象是否包含所有需要的组件和函数
- 确认导入路径是否正确
- 验证代码语法是否正确
- 检查是否使用了 `render()` 函数

### 4.3 预览样式问题
**问题**：组件在预览中样式不正确
**解决**：
- 确认组件使用了 `cn()` 函数处理类名
- 检查 Tailwind CSS 类名是否正确
- 验证组件是否支持 `className` 属性

## 5. 开发工具和脚本

### 5.1 推荐的VS Code扩展
- TypeScript Importer
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Auto Rename Tag
- Bracket Pair Colorizer

### 5.2 有用的开发脚本
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "type-check": "tsc --noEmit",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write ."
  }
}
```

### 5.3 代码片段（VS Code）
创建 `.vscode/snippets.json`：
```json
{
  "React Component": {
    "prefix": "rfc",
    "body": [
      "\"use client\"",
      "",
      "import { ReactNode } from \"react\"",
      "import { cn } from \"@/lib/utils\"",
      "",
      "export interface ${1:Component}Props {",
      "  className?: string",
      "}",
      "",
      "export function ${1:Component}({",
      "  className,",
      "}: ${1:Component}Props) {",
      "  return (",
      "    <div className={cn(\"${2:component}\", className)}>",
      "      ${3:// Component content}",
      "    </div>",
      "  )",
      "}"
    ],
    "description": "Create a React functional component"
  }
}
```

## 6. 质量检查清单

### 6.1 组件开发完成检查
- [ ] 组件具有完整的TypeScript类型定义
- [ ] 所有属性都有JSDoc注释
- [ ] 组件支持className属性
- [ ] 组件具有合理的默认值
- [ ] 代码通过ESLint和TypeScript检查

### 6.2 示例和文档检查
- [ ] 至少包含3个不同的使用示例
- [ ] 示例代码可以独立运行
- [ ] API文档完整且准确
- [ ] 包含"何时使用"的说明
- [ ] 在导航中正确配置

### 6.3 用户体验检查
- [ ] 组件在不同屏幕尺寸下正常显示
- [ ] 组件支持键盘导航（如果适用）
- [ ] 组件具有合适的焦点状态
- [ ] 组件支持暗色模式（如果适用）

通过遵循这个实施指南，你可以快速上手新的组件开发规范，并创建高质量、一致性的组件。
