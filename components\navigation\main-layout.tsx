"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/theme-toggle"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { ChevronDown, ChevronRight } from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { useState, useEffect } from "react"

// 导出NavSection类型
export interface NavSection {
  title: string;
  defaultOpen?: boolean;
  items: {
    title: string;
    href?: string;
    disabled?: boolean;
    isNew?: boolean;
  }[];
}

// 组件导航数据 - 按功能分类重新组织
export const componentSections: NavSection[] = [
  {
    title: "数据展示",
    defaultOpen: true,
    items: [
      {
        title: "数据表格",
        href: "/examples/data-table",
        isNew: true,
      },
      {
        title: "统计卡片",
        href: "/examples/stats-example",
      },
      {
        title: "卡片模板",
        href: "/examples/card-templates-example",
        isNew: true,
      },
      {
        title: "列表视图",
        href: "/examples/list-view-example",
      },
      {
        title: "时间轴",
        href: "/examples/timeline-example",
      },
      {
        title: "图表",
        href: "/examples/chart-example",
      },
    ],
  },
  {
    title: "表单与输入",
    defaultOpen: true,
    items: [
      {
        title: "基础表单",
        href: "/examples/form-example",
      },

      {
        title: "表单对话框",
        href: "/examples/form-dialog-example",
      },
      {
        title: "搜索组件",
        href: "/examples/search-example",
      },
      {
        title: "筛选器",
        href: "/examples/filter-example",
      },
      {
        title: "文件上传",
        href: "/examples/file-upload-example",
      },
    ],
  },
  {
    title: "导航与布局",
    defaultOpen: true,
    items: [
      {
        title: "面包屑导航",
        href: "/examples/breadcrumb-example",
      },
      {
        title: "标签页",
        href: "/examples/tabs-example",
      },
      {
        title: "分页",
        href: "/examples/pagination-example",
      },
      {
        title: "返回按钮",
        href: "/examples/back-button-example",
      },

      {
        title: "仪表板",
        href: "/examples/dashboard-example",
      },
    ],
  },
  {
    title: "交互与反馈",
    defaultOpen: true,
    items: [
      {
        title: "操作按钮",
        href: "/examples/action-buttons-example",
      },
      {
        title: "模态框",
        href: "/examples/modal-example",
      },
      {
        title: "菜单",
        href: "/examples/menu-example",
        isNew: true,
      },
      {
        title: "通知",
        href: "/examples/notification-example",
      },
      {
        title: "工具提示",
        href: "/examples/tooltip-example",
      },
      {
        title: "加载状态",
        href: "/examples/loading-example",
      },
      {
        title: "骨架屏",
        href: "/examples/skeleton-example",
      },
    ],
  },
  {
    title: "内容与状态",
    defaultOpen: true,
    items: [
      {
        title: "标签",
        href: "/examples/tag-example",
      },
      {
        title: "状态徽章",
        href: "/examples/status-badge-example",
      },
      {
        title: "评分",
        href: "/examples/rating-example",
      },
      {
        title: "评论",
        href: "/examples/comment-example",
      },
      {
        title: "文本截断",
        href: "/examples/truncate-text-example",
      },
      {
        title: "空状态",
        href: "/examples/empty-state-example",
      },
      {
        title: "错误状态",
        href: "/examples/error-state-example",
      },
    ],
  },
  {
    title: "工具与扩展",
    defaultOpen: true,
    items: [
      {
        title: "图标选择器",
        href: "/examples/icon-selector-example",
      },
      {
        title: "日历",
        href: "/examples/calendar-example",
        isNew: true,
      },
      {
        title: "组合组件",
        href: "/examples/group-components-example",
      },
    ],
  },
];

// 自定义侧边栏导航，智能展开逻辑
function CustomSidebarNav({ sections }: { sections: NavSection[] }) {
  // 使用React的钩子获取当前路径
  const [pathname, setPathname] = useState<string>("");

  // 找到当前激活的section
  const getActiveSectionTitle = () => {
    for (const section of sections) {
      for (const item of section.items) {
        if (pathname === item.href) {
          return section.title;
        }
      }
    }
    return null;
  };

  const activeSectionTitle = getActiveSectionTitle();

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setPathname(window.location.pathname);

      // 监听路径变化
      const handlePathChange = () => {
        setPathname(window.location.pathname);
      };

      // 监听popstate事件（浏览器前进后退）
      window.addEventListener('popstate', handlePathChange);

      return () => {
        window.removeEventListener('popstate', handlePathChange);
      };
    }
  }, []);

  // 当路径变化时，更新展开状态 - 只展开激活的section
  useEffect(() => {
    if (!pathname) return;

    const newActiveSectionTitle = getActiveSectionTitle();

    if (newActiveSectionTitle) {
      setOpenSections((prev) => {
        const newState: Record<string, boolean> = {};

        // 关闭所有section
        sections.forEach(section => {
          newState[section.title] = false;
        });

        // 只展开激活的section
        newState[newActiveSectionTitle] = true;

        return newState;
      });
    }
  }, [pathname, sections]);

  // 初始化展开状态：最多只展开一个section
  const [openSections, setOpenSections] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    sections.forEach(section => {
      initial[section.title] = false;
    });

    // 如果有激活的section，只展开激活的section
    if (activeSectionTitle) {
      initial[activeSectionTitle] = true;
    } else if (sections.length > 0) {
      // 如果没有激活section，展开第一个section
      initial[sections[0].title] = true;
    }

    return initial;
  });

  // 智能切换section展开状态 - 最多只能激活一个tab
  const toggleSection = (title: string) => {
    setOpenSections((prev) => {
      const newState = { ...prev };
      const isCurrentlyOpen = prev[title];

      if (isCurrentlyOpen) {
        // 如果是当前激活的section，不允许关闭
        if (title === activeSectionTitle) {
          return prev;
        }
        // 关闭当前section
        newState[title] = false;
      } else {
        // 关闭所有其他section，只展开当前section
        Object.keys(newState).forEach(sectionTitle => {
          newState[sectionTitle] = false;
        });

        // 展开当前section
        newState[title] = true;

        // 如果有激活的section且不是当前点击的，也要展开激活的section
        if (activeSectionTitle && activeSectionTitle !== title) {
          newState[activeSectionTitle] = true;
        }
      }

      return newState;
    });
  };

  return (
    <div className="pb-12">
      <div className="space-y-4 py-4">
        <div className="px-4 py-3">
          <div className="mb-1">
            <h2 className="px-2 text-base font-semibold tracking-tight">
              业务组件库
            </h2>
          </div>
          <div className="px-2 text-xs text-muted-foreground">
            基于 shadcn/ui 构建的业务组件
          </div>
        </div>
        <Separator className="my-1" />
        <ScrollArea className="h-[calc(100vh-10rem)] px-2">
          <div className="space-y-1 p-2">
            {sections.map((section) => (
              <Collapsible
                key={section.title}
                open={openSections[section.title]}
                onOpenChange={() => toggleSection(section.title)}
                className="space-y-1"
              >
                <CollapsibleTrigger
                  className={cn(
                    "flex w-full items-center justify-between rounded-md px-4 py-2 text-sm font-medium hover:bg-muted",
                    // 如果当前section包含激活的菜单项，则高亮父级菜单
                    section.title === activeSectionTitle
                      ? "text-primary bg-muted/50 border-l-2 border-primary"
                      : "text-foreground"
                  )}
                >
                  <span>{section.title}</span>
                  <div>
                    {openSections[section.title] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1">
                  {section.items.map((item) => (
                    <Link
                      key={item.href}
                      href={item.disabled || !item.href ? "#" : item.href}
                      className={cn(
                        "flex items-center justify-between rounded-md px-4 py-2 text-sm font-medium hover:bg-muted",
                        pathname === item.href
                          ? "bg-muted font-medium text-primary"
                          : "text-muted-foreground",
                        item.disabled && "cursor-not-allowed opacity-60"
                      )}
                    >
                      <span>{item.title}</span>
                      {item.isNew && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          新
                        </Badge>
                      )}
                    </Link>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

interface MainLayoutProps {
  children: React.ReactNode
  /**
   * 自定义导航项，如果提供则使用自定义导航而不是默认的componentSections
   */
  sections?: NavSection[]
  /**
   * 当前页面标题，用于显示在页头
   */
  pageTitle?: string
}

export function MainLayout({ children, sections, pageTitle = "业务组件库" }: MainLayoutProps) {
  // 使用提供的sections或默认的componentSections
  const navSections = sections || componentSections;
  
  return (
    <div className="flex min-h-screen">
      {/* 侧边栏 */}
      <aside className="fixed inset-y-0 left-0 z-10 hidden w-[280px] border-r border-border bg-background lg:block">
        <CustomSidebarNav sections={navSections} />
      </aside>
      
      {/* 主内容区域 */}
      <div className="flex flex-col lg:pl-[280px] w-full">
        <header className="sticky top-0 z-30 flex h-14 items-center border-b border-border bg-background px-6 sm:px-8">
          <div className="flex flex-1 items-center gap-2">
            <div className="flex-1">
              <h1 className="text-lg font-semibold">{pageTitle}</h1>
            </div>
            <ModeToggle />
          </div>
        </header>
        <main className="flex-1 overflow-auto">
          <div className="max-w-[1440px] w-full mx-auto py-5 px-6 sm:px-8">
            {children}
          </div>
        </main>
        <footer className="border-t border-border py-6 px-6 sm:px-8">
          <div className="flex items-center justify-center">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} 业务组件库
            </p>
          </div>
        </footer>
      </div>
    </div>
  )
} 
