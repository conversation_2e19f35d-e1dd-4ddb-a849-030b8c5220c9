# MCP工具使用规范

## 🎯 核心原则

在解决问题或实现功能时，应该合理、高效地使用MCP工具，遵循以下原则：

### 1. 工具优先级原则
- **优先使用专门的MCP工具**，而不是通用方法
- **组合使用多个工具**以实现复杂功能
- **避免重复造轮子**，充分利用现有工具能力

### 2. 效率最大化原则
- **批量操作优于单次操作**
- **并行处理优于串行处理**
- **缓存结果优于重复计算**

## 🛠️ 各工具使用规范

### 📁 filesystem - 文件系统操作
**使用场景：**
- 读取、写入、搜索本地文件
- 目录结构分析
- 文件内容检索

**最佳实践：**
```javascript
// ✅ 正确：批量读取相关文件
filesystem.read_multiple_files([
  "src/components/Button.tsx",
  "src/components/Input.tsx",
  "src/styles/components.css"
])

// ❌ 错误：逐个读取文件
filesystem.read_file("src/components/Button.tsx")
filesystem.read_file("src/components/Input.tsx")
filesystem.read_file("src/styles/components.css")
```

### 🧠 sequential-thinking - 结构化思考
**使用场景：**
- 复杂问题分析
- 多步骤解决方案规划
- 逻辑推理和决策

**最佳实践：**
```javascript
// ✅ 正确：用于复杂问题分解
sequential_thinking({
  problem: "设计一个可扩展的组件库架构",
  steps: [
    "分析现有组件结构",
    "识别通用模式",
    "设计抽象层",
    "制定实施计划"
  ]
})

// ❌ 错误：用于简单问题
sequential_thinking({
  problem: "修改按钮颜色"
})
```

### 🌐 fetch-mcp - 网络请求
**使用场景：**
- 获取外部API数据
- 下载网页内容
- 访问在线资源

**最佳实践：**
```javascript
// ✅ 正确：获取最新技术文档
fetch_mcp.get("https://api.github.com/repos/facebook/react/releases/latest")

// ✅ 正确：获取网页内容进行分析
fetch_mcp.get("https://developer.mozilla.org/en-US/docs/Web/CSS/flexbox")
```

### 🎭 playwright - 浏览器自动化
**使用场景：**
- 网页测试和验证
- 动态内容抓取
- 用户界面交互测试

**最佳实践：**
```javascript
// ✅ 正确：测试完整用户流程
playwright.navigate("http://localhost:3000")
playwright.fill("#username", "testuser")
playwright.fill("#password", "testpass")
playwright.click("#login-button")
playwright.screenshot("login-success")

// ❌ 错误：仅用于静态内容获取（应使用fetch-mcp）
playwright.navigate("https://example.com")
playwright.get_content()
```

### 💬 mcp-feedback-enhanced - 用户交互
**使用场景：**
- 需要用户确认的操作
- 收集用户偏好和选择
- 展示中间结果供用户审核

**最佳实践：**
```javascript
// ✅ 正确：重要操作前确认
mcp_feedback_enhanced.request_feedback({
  message: "即将删除以下文件，请确认：\n- old-component.tsx\n- unused-styles.css",
  options: ["确认删除", "取消操作", "仅删除组件文件"]
})

// ✅ 正确：展示设计选项
mcp_feedback_enhanced.request_feedback({
  message: "请选择组件库的设计风格：",
  options: ["Material Design", "Ant Design", "Chakra UI", "自定义设计"]
})
```

### 🧠 memory - 持久化记忆
**使用场景：**
- 存储项目相关信息
- 记录用户偏好
- 缓存计算结果

**最佳实践：**
```javascript
// ✅ 正确：存储项目配置
memory.store({
  key: "project_preferences",
  value: {
    codeStyle: "typescript",
    framework: "react",
    styling: "tailwindcss"
  }
})

// ✅ 正确：记录常用模式
memory.store({
  key: "common_patterns",
  value: ["hooks", "context", "compound_components"]
})
```

### 🔍 context7 - 代码上下文
**使用场景：**
- 获取相关代码片段
- 理解代码库结构
- 查找相似实现

**最佳实践：**
```javascript
// ✅ 正确：查找相关组件实现
context7.search({
  query: "button component with loading state",
  type: "implementation"
})

// ✅ 正确：获取API使用示例
context7.search({
  query: "useState hook examples",
  type: "usage_patterns"
})
```

## 🔄 工具组合使用模式

### 模式1：分析-思考-实施
```javascript
// 1. 分析现状
const files = filesystem.read_multiple_files(["src/**/*.tsx"])

// 2. 结构化思考
const plan = sequential_thinking({
  problem: "重构组件库",
  context: files
})

// 3. 用户确认
const approval = mcp_feedback_enhanced.request_feedback({
  message: "重构计划如下，是否继续？",
  details: plan
})

// 4. 执行实施
if (approval.confirmed) {
  filesystem.write_file("refactor-plan.md", plan)
}
```

### 模式2：研究-验证-应用
```javascript
// 1. 研究最佳实践
const docs = fetch_mcp.get("https://react.dev/learn/thinking-in-react")

// 2. 查找相关代码
const examples = context7.search({
  query: "react component patterns",
  type: "examples"
})

// 3. 验证实现
playwright.navigate("http://localhost:3000/components")
playwright.screenshot("current-implementation")

// 4. 记录发现
memory.store({
  key: "research_findings",
  value: { docs, examples, screenshot: "current-implementation" }
})
```

### 模式3：迭代-反馈-优化
```javascript
// 1. 实现初版
filesystem.write_file("src/components/NewComponent.tsx", initialCode)

// 2. 获取反馈
const feedback = mcp_feedback_enhanced.request_feedback({
  message: "请审核新组件实现",
  code: initialCode,
  options: ["通过", "需要修改", "重新设计"]
})

// 3. 根据反馈优化
if (feedback.action === "需要修改") {
  const improvements = sequential_thinking({
    problem: "优化组件实现",
    feedback: feedback.details
  })
  
  // 4. 应用改进
  filesystem.write_file("src/components/NewComponent.tsx", improvedCode)
}
```

## ⚠️ 注意事项

### 避免的反模式

1. **过度使用工具**
   - 不要为了使用工具而使用工具
   - 简单问题用简单方法解决

2. **忽略用户体验**
   - 重要操作必须使用mcp-feedback-enhanced确认
   - 提供清晰的操作说明和选项

3. **低效的工具组合**
   - 避免不必要的重复操作
   - 合理安排工具调用顺序

4. **忽略错误处理**
   - 每个工具调用都应该有错误处理
   - 提供备选方案

### 性能优化建议

1. **批量操作**
   - 使用filesystem的批量读写功能
   - 合并相关的网络请求

2. **缓存利用**
   - 使用memory存储常用数据
   - 避免重复的expensive操作

3. **并行处理**
   - 独立的操作可以并行执行
   - 合理利用异步特性

## 📋 检查清单

在使用MCP工具前，请检查：

- [ ] 是否选择了最合适的工具？
- [ ] 是否可以批量处理？
- [ ] 是否需要用户确认？
- [ ] 是否需要缓存结果？
- [ ] 是否有错误处理？
- [ ] 是否考虑了用户体验？

遵循这些规范，可以最大化MCP工具的效用，提供更好的开发体验和更高的工作效率。
