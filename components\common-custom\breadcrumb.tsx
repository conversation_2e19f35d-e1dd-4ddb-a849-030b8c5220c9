"use client"

import React from "react"
import {
  Breadcrumb as UIBreadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
} from "@/components/ui/breadcrumb"
import { ChevronRight, Home, LucideIcon } from "lucide-react"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 面包屑导航项
 */
export interface BreadcrumbItemType {
  /**
   * 链接地址
   */
  href?: string

  /**
   * 显示标签
   */
  label: string

  /**
   * 是否为当前页面
   */
  isCurrent?: boolean

  /**
   * 是否为当前页面（兼容性）
   */
  current?: boolean

  /**
   * 图标
   */
  icon?: React.ReactNode | LucideIcon

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 面包屑导航组件属性
 */
export interface BreadcrumbExtendedProps {
  /**
   * 导航项列表
   */
  items: BreadcrumbItemType[]

  /**
   * 显示变体
   * @default "standard"
   */
  variant?: "standard" | "collapsed" | "simple"

  /**
   * 最大显示项数（超出时折叠）
   * @default 3
   */
  maxItems?: number

  /**
   * 自定义分隔符
   */
  separator?: React.ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 导航回调函数
   */
  onNavigate?: (href: string, e: React.MouseEvent) => void

  /**
   * 是否显示首页图标
   * @default false
   */
  showHomeIcon?: boolean

  /**
   * 首页图标
   */
  homeIcon?: LucideIcon

  /**
   * 是否显示当前页面
   * @default true
   */
  showCurrentPage?: boolean

  /**
   * 分隔符样式
   * @default "chevron"
   */
  separatorStyle?: "chevron" | "slash" | "dot" | "custom"

  /**
   * 尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"
}

export function Breadcrumb({
  items,
  variant = 'standard', 
  maxItems = 3,
  separator,
  className = '',
  onNavigate,
}: BreadcrumbExtendedProps) {
  // 处理导航项
  const processedItems = items.map(item => ({
    ...item,
    isCurrent: item.current || item.isCurrent
  }));

  // 处理折叠逻辑
  const displayItems = variant === 'collapsed' && processedItems.length > maxItems
    ? [
        // 第一项始终显示
        processedItems[0],
        // 中间使用省略号
        { label: '...', href: '#', isCurrent: false },
        // 显示最后的几项
        ...processedItems.slice(-Math.min(maxItems - 1, processedItems.length - 1))
      ]
    : processedItems;

  const handleClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    if (onNavigate) {
      onNavigate(href, e);
    } else {
      // 默认行为，如果没有提供导航回调函数
      window.location.href = href;
    }
  };

  return (
    <UIBreadcrumb className={className}>
      <BreadcrumbList className="flex flex-nowrap items-center space-x-2 text-sm">
        {displayItems.map((item, index) => (
          <React.Fragment key={`breadcrumb-item-${index}`}>
            <BreadcrumbItem className="flex-shrink-0 inline-flex items-center">
              {item.label === '...' ? (
                <BreadcrumbEllipsis />
              ) : item.isCurrent ? (
                <BreadcrumbPage className="flex items-center gap-1 truncate max-w-[120px] md:max-w-[240px] font-medium text-foreground">
                  {item.icon && item.icon} {item.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  href={item.href || "#"}
                  onClick={(e) => item.href ? handleClick(item.href, e) : undefined}
                  className="flex items-center gap-1 truncate max-w-[100px] md:max-w-[200px] text-muted-foreground hover:text-foreground hover:underline transition-colors"
                >
                  {item.icon && item.icon} {item.label}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < displayItems.length - 1 && (
              <BreadcrumbSeparator>
                {separator || <ChevronRight className="h-4 w-4 opacity-50" />}
              </BreadcrumbSeparator>
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </UIBreadcrumb>
  );
}

// ============================================================================
// 类型导出
// ============================================================================

export type { BreadcrumbItemType, BreadcrumbExtendedProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的面包屑变体
 */
export const BREADCRUMB_VARIANTS = ['standard', 'collapsed', 'simple'] as const

/**
 * 支持的分隔符样式
 */
export const SEPARATOR_STYLES = ['chevron', 'slash', 'dot', 'custom'] as const

/**
 * 支持的尺寸
 */
export const BREADCRUMB_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 默认面包屑配置
 */
export const DEFAULT_BREADCRUMB_CONFIG = {
  variant: 'standard' as const,
  maxItems: 3,
  showHomeIcon: false,
  showCurrentPage: true,
  separatorStyle: 'chevron' as const,
  size: 'md' as const,
} satisfies Partial<BreadcrumbExtendedProps>

/**
 * 常用面包屑模式
 */
export const BREADCRUMB_PATTERNS = {
  simple: { variant: 'standard' as const, maxItems: 5 },
  compact: { variant: 'collapsed' as const, maxItems: 3 },
  detailed: { variant: 'standard' as const, showHomeIcon: true },
} as const
